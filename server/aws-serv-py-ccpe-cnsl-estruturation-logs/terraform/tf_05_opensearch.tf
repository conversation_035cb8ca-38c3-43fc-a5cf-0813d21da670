data "aws_iam_policy_document" "opensearch_access" {
  statement {
    effect = "Allow"
    actions = [
      "es:HttpPut",
      "es:HttpPost",
      "es:HttpGet",
      "es:HttpHead"
    ]
    resources = [
      "arn:aws:es:us-east-1:405689464555:domain/falhas-ts-esteiras/*"
    ]
  }
}

resource "aws_iam_policy" "lambda_opensearch_policy" {
  name   = "LambdaOpensearchPolicy"
  policy = data.aws_iam_policy_document.opensearch_access.json
}