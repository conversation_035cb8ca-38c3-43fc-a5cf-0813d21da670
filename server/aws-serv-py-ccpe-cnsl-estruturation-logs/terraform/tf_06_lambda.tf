module "Exemplo_lambda" {
  source = "git::https://gitportoprd.portoseguro.brasil/cpe/containers-serverless/modules/serverless/lambda.git?ref=v1.3"

  aws_regiao_conta=var.aws_regiao_conta
  managed_policy_arns=[
    data.aws_iam_policy.lambda_vpc_policy.arn,
    data.aws_iam_policy.lambda_secret.arn,
    aws_iam_policy.lambda_opensearch_policy.arn,
    "arn:aws:iam::405689464555:policy/logs-erro-bucket-policy"
  ]

  function_name = "EstruturacaoLogs"
  runtime="python3.12"
  handler = "lambda.lambda_handler"
  memory_size="256"
  timeout="30"

  # Habilita acesso à VPC caso sua lambda precise acessar algum recurso dentro da Porto ou da propria VPC
  configure_vpc_access=true
  cidr_block=var.cidr

  # Habilita o evento de Stream de Dynamo Table
  #dynamodb_trigger_event={
  #  enabled=true
  #  dynamodb_trigger_stream_arn=data.aws_dynamodb_table.dynamodb_table.stream_arn
  #}

  # Habilita evento de SQS
  # Pode-se utilizar um SQS já existente mantendo create_sqs=false e passando sqs_arn.
  # Ou pode-se criar um novo SQS passando create_sqs=true e name=nome_da_sua_fila
  #sqs = {
  #  enabled = "false"
  #  create_sqs = "false"
  #  name=""
  #  sqs_arn = ""
  #}

  ## Habilita Scheduler para Lambda
  # scheduler = {
  #   enabled = true
  #   expression = "rate(1 day)" #ou cron(* * * * *)
  #   input=" input que sera recebido na lambda!"
  # }
}