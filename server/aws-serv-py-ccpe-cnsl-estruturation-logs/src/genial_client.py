import requests
from auth_genial import obter_token_genial
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def enviar_para_ia_genial(conteudo_html):
    token = obter_token_genial()
    url = "https://apis-hml.portoseguro.brasil/corp/genial/v1/f3abbd03-6a99-4e57-a861-baf9698dbd0e/chat"

    data = {
        "variantName": "indicadores",
        "prompt": conteudo_html
    }

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }

    response = requests.post(url, json=data, headers=headers, verify=False)
    response.raise_for_status()

    return response.json()["message"]["content"]