from opensearchpy import OpenSearch, RequestsHttpConnection
from requests_aws4auth import <PERSON>WS4Auth
import boto3

REGIAO = "us-east-1"
DOMINIO_ENDPOINT = "vpc-falhas-ts-esteiras-2y2mgunrf1gyij2yufog5oru3m.us-east-1.es.amazonaws.com"
INDEX_NAME = "logs-pipelines-ci"


def get_opensearch_client():
    credentials = boto3.Session().get_credentials()
    awsauth = AWS4Auth(
        credentials.access_key,
        credentials.secret_key,
        REGIAO,
        "es",
        session_token=credentials.token
    )

    return OpenSearch(
        hosts=[{"host": DOMINIO_ENDPOINT, "port": 443}],
        http_auth=awsauth,
        use_ssl=True,
        verify_certs=True,
        connection_class=RequestsHttpConnection
    )


def salvar_documento(documento: dict):
    client = get_opensearch_client()

    if not client.indices.exists(index=INDEX_NAME):
        client.indices.create(index=INDEX_NAME)

    doc_id = documento.get("component_name")
    if not doc_id:
        raise ValueError("Campo 'component_name' é obrigatório como chave do documento")

    response = client.index(index=INDEX_NAME, id=doc_id, body=documento)
    print(f"Documento salvo no OpenSearch com ID: {doc_id}")
    return response