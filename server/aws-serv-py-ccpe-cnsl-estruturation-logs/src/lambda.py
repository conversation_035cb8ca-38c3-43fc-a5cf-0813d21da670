import requests
import os
import json
from limpar_html import limpar_html_visivel
from limpar_resposta_ia import limpar_resposta_ia
from s3_handler import download_html_from_s3
from genial_client import enviar_para_ia_genial
from opensearch_handler import salvar_documento

# Define constants
BUCKET_NAME = "logs-erro-esteiras-techstore"
OBJECT_KEY = "logs_com_erro_CI_angular-auto-conv-novaemissao_376411.html"
LOCAL_PATH = "/tmp/temp_arquivo.html"
CHAR_LIMIT = 100_000

def main():
    download_html_from_s3(BUCKET_NAME, OBJECT_KEY, LOCAL_PATH)

    with open(LOCAL_PATH, "r", encoding="utf-8") as f:
        conteudo_html = f.read()

    texto_visivel = limpar_html_visivel(conteudo_html)
    texto_limitado = texto_visivel[:CHAR_LIMIT]

    resultado_str = enviar_para_ia_genial(texto_limitado)
    print("Resultado da IA:")
    print(resultado_str)

    try:
        json_string = limpar_resposta_ia(resultado_str)
        resultado_dict = json.loads(json_string)

        if not resultado_dict.get("component_name"):
            raise ValueError("Campo 'component_name' ausente no JSON da IA")

        salvar_documento(resultado_dict)

    except json.JSONDecodeError as e:
        print(f"Erro ao converter resultado para JSON: {e}")
    except Exception as e:
        print(f"Erro ao salvar no OpenSearch: {e}")
    finally:
        os.remove(LOCAL_PATH)

def lambda_handler(event, context):
    main()