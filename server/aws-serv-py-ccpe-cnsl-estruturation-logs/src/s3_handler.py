import boto3
from botocore.exceptions import NoCredentialsError

def download_html_from_s3(bucket_name, object_key, local_path):
    s3 = boto3.client('s3')

    try:
        s3.download_file(bucket_name, object_key, local_path)
        print(f"Arquivo {object_key} baixado com sucesso para {local_path}")
    except NoCredentialsError:
        print("Credenciais da AWS não encontradas.")
        raise
    except Exception as e:
        print(f"Erro ao baixar arquivo do S3: {e}")
        raise