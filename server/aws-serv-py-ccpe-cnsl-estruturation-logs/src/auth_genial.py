import requests
import os
from dotenv import load_dotenv
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

load_dotenv()

client_id = os.environ["GENIAL_SSO_CLIENT_ID"]
client_secret = os.environ["GENIAL_SSO_CLIENT_SECRET"]

def obter_token_genial():
    url = "https://sso-interno.authcorp.hml.awsporto/auth/realms/mulesoft/protocol/openid-connect/token"
    payload = {
        "grant_type": "client_credentials",
        "client_id": client_id,
        "client_secret": client_secret
    }
    headers = {
        "Host": "sso-corp-hml.portoseguro.com.br",
        "Content-Type": "application/x-www-form-urlencoded"
    }

    response = requests.post(url, data=payload, headers=headers, verify=False)
    response.raise_for_status()

    return response.json()["access_token"]