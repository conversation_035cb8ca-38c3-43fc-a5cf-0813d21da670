from bs4 import BeautifulSoup

def limpar_html_visivel(html):
    soup = BeautifulSoup(html, 'html.parser')

    # Remove elementos que não são conteúdo
    for tag in soup(['script', 'style', 'meta', 'noscript', 'iframe', 'link']):
        tag.decompose()

    # Extrai texto visível
    texto = soup.get_text(separator=' ', strip=True)

    # Normaliza espaços
    texto = ' '.join(texto.split())
    return texto