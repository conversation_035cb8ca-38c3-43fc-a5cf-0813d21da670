import json
import boto3
import requests
from opensearchpy import OpenSearch, RequestsHttpConnection
from botocore.exceptions import ClientError
import logging
import os
from dotenv import load_dotenv

load_dotenv()

# Configurações
AWS_REGION = 'ca-central-1'
S3_BUCKET_NAME = 'logs-erro-esteiras-techstore'
S3_PREFIX = 'logs_com_erro_ci/'

GENIAL_API_URL = 'https://apis-hml.portoseguro.brasil/corp/genial/v1/f3abbd03-6a99-4e57-a861-baf9690dbd0e/chat'
GENIAL_TOKEN_URL = 'https://sso-interno.authcorp.hml.awsporto/auth/realms/mulesoft/protocol/openid-connect/token'
GENIAL_CLIENT_ID = '75afe64-16ea-485b-b47b-475dbea31d79'
GENIAL_CLIENT_SECRET = os.environ["GENIAL_SSO_CLIENT_SECRET"]

OPENSEARCH_HOST = 'vpc-falhas-ts-esteiras-2y2mgunrlfgyj2yufog5orum.us-east-1.es.amazonaws.com'
OPENSEARCH_INDEX = 'logs-jenkins-ci'

# Inicializar boto3 s3 client
s3 = boto3.client('s3', region_name=AWS_REGION)

def listar_arquivos_html():
    paginator = s3.get_paginator('list_objects_v2')
    arquivos = []
    for page in paginator.paginate(Bucket=S3_BUCKET_NAME, Prefix=S3_PREFIX):
        for obj in page.get('Contents', []):
            key = obj['Key']
            if key.endswith('.html'):
                arquivos.append(key)
    return arquivos

def baixar_arquivo_html(key):
    try:
        response = s3.get_object(Bucket=S3_BUCKET_NAME, Key=key)
        return response['Body'].read().decode('utf-8')
    except ClientError as e:
        logging.error(f'Erro ao baixar {key}: {e}')
        return None

def enviar_para_ia(conteudo_html):
    try:
        # Obter Token
        payload = {
            'grant_type': 'client_credentials',
            'client_id': GENIAL_CLIENT_ID,
            'client_secret': GENIAL_CLIENT_SECRET
        }
        headers = {
            'Host': 'sso-corp-hml.portoseguro.com.br',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        response = requests.post(GENIAL_TOKEN_URL, data=payload, headers=headers, verify=False)
        response.raise_for_status()
        token = response.json()['access_token']

        # Enviar para a API Genial
        MAX_CHARS = 10000
        conteudo_cortado = conteudo_html[-MAX_CHARS:]
        data = {
            "variantName": "indicadores",
            "prompt": conteudo_cortado
        }
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}"
        }
        response = requests.post(GENIAL_API_URL, json=data, headers=headers, verify=False)
        response.raise_for_status()

        print(f"Resultado Genial: \n {response.json()['message']['content']}")
        return response.json()['message']['content']
    except Exception as e:
        logging.error(f'Erro ao processar HTML: {e}')
        return None

def salvar_no_opensearch(doc_id, dados_json):
    try:
        secret_name = "falhas-ts-esteiras-master-user-81gw"
        region_name = "us-east-1"
        session = boto3.session.Session()
        client = session.client(
            service_name='secretsmanager',
            region_name=region_name
        )
        try:
            get_secret_value_response = client.get_secret_value(
                SecretId=secret_name
            )
        except ClientError as e:
            raise e

        secret = json.loads(get_secret_value_response['SecretString'])
        auth = (secret['username'], secret['password'])

        client = OpenSearch(
            hosts = [{'host': OPENSEARCH_HOST, 'port': 443}],
            http_compress = True,
            http_auth = auth,
            use_ssl = True,
            verify_certs = True,
            ssl_assert_hostname = False,
            ssl_show_warn = False,
        )

        client.index(index=OPENSEARCH_INDEX, id=doc_id, body=dados_json)
        print(f'Documento salvo no OpenSearch com ID: {doc_id}')
    except Exception as e:
        logging.error(f'Erro ao salvar no OpenSearch ({doc_id}): {e}')

def processar_todos_os_logs():
    arquivos = listar_arquivos_html()
    logging.info(f'{len(arquivos)} arquivos encontrados.')

    arquivos = [key for key in arquivos if os.path.basename(key).startswith('CI_was')]

    for key in arquivos:
        logging.info(f'Processando: {key}')
        html = baixar_arquivo_html(key)
        if not html:
            continue

        json_estruturado = enviar_para_ia(html)
        if not json_estruturado:
            continue
        
        doc_id = os.path.basename(key).replace('.html', '')
        salvar_no_opensearch(doc_id, json_estruturado)

if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO)
    processar_todos_os_logs()