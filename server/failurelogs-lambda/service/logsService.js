const genial = require('./../integration/genial');
const logsOpenSearch = require('./../repository/logsOpenSearch');

class logsService {
  async buscarInsights(payload) {
    try {
      const query = (await genial.obterResposta(payload.pergunta, 'sqlBuilder'))
        .replace(/```json|```/g, "").trim();
      const resultado = await logsOpenSearch.executarQuery(query);
      const conclusao = await genial.obterResposta(JSON.stringify(resultado), 'OpnSrchTranslate');

      const retorno = {
        conclusao,
        resultado,
        queryGerada: JSON.parse(query)
      };

      return retorno;
    } catch (e) {
      throw e;
    }
  }
}

module.exports = new logsService();