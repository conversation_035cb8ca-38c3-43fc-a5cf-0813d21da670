const default: axios = require("axios");
const qs = require('qs');

class genial {
  async authGenial() {
    let data = {
      'grant_type': 'client_credentials',
      'client_id': "75afe564-16ea-4853-b47b-475d3ea31d79",
      'client_secret': "ROR09mtvjMKKzBb0sD0Yhty5VxD1LCg"
    };

    let headers = {
      "Host": "sso-corp-hml.portoseguro.com.br",
      "content-type": "application/x-www-form-urlencoded"
    };

    const url = "https://sso-interno.authcorp.hml.awsporto/auth/realms/mulesoft/protocol/openid-connect/token";
    const res = await axios({
      url: url,
      method: "POST",
      headers: headers,
      data: qs.stringify(data),
    });

    let token = res.data.access_token;
    return token;
  }

  async obterResposta(payload, variante) {
    let genRes = await axios({
      url: 'https://apis-hml.portoseguro.brasil/corp/genial/v1/f3abbd03-6a99-4e57-a861-baf9698dbd0e/chat',
      method: "POST",
      headers: {
        'content-type': 'application/json',
        'authorization': `Bearer ${await this.authGenial()}`
      },
      data: {
        "variantName": variante,
        "prompt": payload
      }
    });

    return genRes.data.message.content;
  }
}

module.exports = new genial();