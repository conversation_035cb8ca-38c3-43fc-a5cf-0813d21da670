const { SecretsManagerClient, GetSecretValueCommand } = require('@aws-sdk/client-secrets-manager');
const { Client } = require('@opensearch-project/opensearch');

class logsOpenSearch {
  constructor() {
    this.opensearchClient = null;
    this.secretName = 'falhas-ts-esteiras-master-8lgw';
    this.domainEndpoint = 'https://vpc-falhas-ts-esteiras-2y2mgunrfgyij2yufoq5oru3m.us-east-1.es.amazonaws.com';
    this.region = 'us-east-1';
  }

  async getSecret() {
    const secretsClient = new SecretsManagerClient({ region: this.region });
    const command = new GetSecretValueCommand({ SecretId: this.secretName });
    const response = await secretsClient.send(command);
    return JSON.parse(response.SecretString);
  }

  async initClient() {
    if (!this.opensearchClient) {
      const credentials = await this.getSecret();
      this.opensearchClient = new Client({
        node: this.domainEndpoint,
        auth: {
          username: credentials.username,
          password: credentials.password
        },
        ssl: {
          rejectUnauthorized: false
        }
      });
    }
  }

  async executarQuery(query, indexName = 'logs-jenkins-ci') {
    console.log(query);
    try {
      await this.initClient();

      const response = await this.opensearchClient.search({
        index: indexName,
        body: query
      });

      console.dir(response.body, { depth: null });
      return response.body;
    } catch (error) {
      console.error('Erro ao executar a query:', error);
      throw error;
    }
  }
}

module.exports = new logsOpenSearch();