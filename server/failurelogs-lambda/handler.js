const logsService = require('./../service/logsService');
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0'; // This line might be related to SSL certificate issues, be cautious in production.

exports.handler = async function (event, context) {
  let statusCode = 0;
  let body = "";
  try {
    let payload = JSON.parse(event.body);
    let retorno = await logsService.buscarInsights(payload);
    statusCode = 200;
    body = JSON.stringify(retorno);
  } catch (error) {
    statusCode = 502;
    body = error.message;
    console.log(error);
  }

  return {
    statusCode: statusCode,
    body: body,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "OPTIONS, POST",
      "Access-Control-Allow-Headers": "content-type",
    },
  };
};

console.log(logsService.buscarInsights({ pergunta: "Me traga as falhas por estágio da pipeline" }).then());