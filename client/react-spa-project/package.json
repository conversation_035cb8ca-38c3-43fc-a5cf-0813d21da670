{"name": "@porto/react-spa-ccpe-cnsl-insights-execucoes", "aplicacao": "react-spa-ccpe-cnsl-insights-execucoes", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "lint": "next lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.11.0", "@mui/material": "^5.11.0", "@mui/x-date-pickers": "^6.3.1", "axios": "^1.4.0", "next": "14.2.3", "oidc-client-ts": "^2.2.1", "react": "18", "react-dom": "18", "react-markdown": "^8.0.7", "react-oidc-context": "^2.3.0", "react-syntax-highlighter": "^15.5.0", "styled-components": "^5.3.6"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@types/jest": "^29.5.12", "@types/node": "^18", "@types/react": "18", "@types/react-dom": "18", "@types/react-syntax-highlighter": "^15.5.7", "eslint": "^8", "eslint-config-next": "14.2.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "ts-node": "^10.9.2", "typescript": "^5"}, "engines": {"node": ">=20.12.2"}}