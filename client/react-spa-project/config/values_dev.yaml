# https://portoseguro.atlassian.net/wiki/spaces/CPE/pages/977600515/MA+-+Configura+o+do+arquivo+de+Values
appFramework: react

ingress:
  enabled: true
  contextURI: /react-spa-ccpe-cnsl-insights-execucoes
  host: ""

namespace: ccpe-cnsl
containerPort: 80

hpa:
  enabled: true
  enableMemory: false
  enableCpu: true
  minReplicas: 1
  maxReplicas: 3
  targetMemoryUtilizationPercentage: 85
  targetCpuUtilizationPercentage: 85

resources:
  cpu: 50m
  memory: 200Mi

# Habilita ou não as secrets que são "puxadas" através do Backstage
enable_secrets: false

configmap:
  profile: "dev"