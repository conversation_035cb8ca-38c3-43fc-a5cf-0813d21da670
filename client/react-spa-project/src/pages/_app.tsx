"use client";

import { ThemeProvider } from "@mui/material";
import { AppProps } from "next/app";
import { AuthProvider } from "react-oidc-context";
import { onSigninCallback, userManager } from "@/config/auth.config";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { PortoTheme } from "@/shared/themes/Porto";
import { Layout } from "@/components/Layout";
import Head from "next/head";

export default function App({ Component, pageProps }: AppProps) {
  return (
    <>
      <Head>
        <title>Insights Operacionais</title>
        <link
          rel="icon"
          href="/react-spa-ccpe-cnsl-insights-execucoes/porto.png"
        />
      </Head>
      <AuthProvider
        userManager={userManager}
        onSigninCallback={onSigninCallback}
      >
        <ThemeProvider theme={PortoTheme}>
          <LocalizationProvider adapterLocale="pt">
            <Layout>
              <Component {...pageProps} />
            </Layout>
          </LocalizationProvider>
        </ThemeProvider>
      </AuthProvider>
    </>
  );
}
