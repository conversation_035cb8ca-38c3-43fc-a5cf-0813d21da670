import { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Typography,
  CircularProgress,
  Container,
  Box,
  Paper,
  Divider,
  IconButton,
  CssBaseline,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tooltip,
  LinearProgress,
} from "@mui/material";
import {
  Send as SendIcon,
  ExpandMore as ExpandMoreIcon,
  ArticleOutlined,
  CodeOutlined,
  DoneAllOutlined,
} from "@mui/icons-material";
import axios from "axios";
import ReactMarkdown from "react-markdown";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/cjs/styles/prism";

export default function NaturalQueryPage() {
  const [question, setQuestion] = useState("");
  const [response, setResponse] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleAsk = async () => {
    setLoading(true);
    setResponse(null);
    setError(null);

    try {
      const res = await axios.post(
        "https://khmvy7okl2.vpce-07da0d7a1d7ccdac0.execute-api.us-east-1.amazonaws.com/v1/opensearch",
        {
          pergunta: question,
        }
      );

      if (res.status !== 200) throw new Error("Erro ao consultar a API");

      setResponse(res.data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCopiarTexto = (textoToCopy: string) => {
    navigator.clipboard
      .writeText(textoToCopy)
      .then(() => console.log("Copiado!"))
      .catch((err) => console.error("Erro ao copiar: ", err));
  };

  return (
    <>
      <CssBaseline />
      <Container sx={{ mt: 6, width: "md" }}>
        <Paper elevation={3} sx={{ p: 5 }}>
          <Typography variant="h4" color="primary" gutterBottom>
            Porto Tech Store - Consulta de Falhas
          </Typography>
          <Typography variant="body1" color="text.secondary" gutterBottom>
            Esta aplicação converte perguntas em linguagem natural em consultas
            OpenSearch, buscando informações sobre falhas ocorridas nas
            execuções das esteiras Jenkins.
          </Typography>
          <Divider sx={{ my: 3 }} />

          <Box display="flex" gap={2}>
            <TextField
              fullWidth
              label="Digite sua pergunta"
              variant="outlined"
              value={question}
              onChange={(e) => setQuestion(e.target.value)}
              onKeyDown={(e) => e.key === "Enter" && handleAsk()}
            />
            <IconButton
              color="primary"
              onClick={handleAsk}
              disabled={loading || !question.trim()}
            >
              <SendIcon />
            </IconButton>
          </Box>

          {loading && (
            <Box mt={5} textAlign="center">
              <LinearProgress />
              <Typography mt={2} color="text.secondary">
                Consultando a IA e a base de dados do OpenSearch...
              </Typography>
            </Box>
          )}

          {error && (
            <Typography mt={5} color="error">
              {error}
            </Typography>
          )}

          {response && (
            <Box
              sx={{ mt: 4, display: "flex", flexDirection: "column", gap: 2 }}
            >
              {/* Conclusão */}
              <Accordion
                defaultExpanded={true}
                disableGutters
                sx={{
                  borderRadius: 2,
                  backgroundColor: "transparent",
                  color: "#fff",
                  boxShadow: "none",
                  "&.Mui-expanded": { backgroundColor: "#1e1e1e" },
                }}
              >
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon sx={{ color: "#fff" }} />}
                  sx={{
                    backgroundColor: "#1e1e1e",
                    borderRadius: 2,
                    "& .MuiAccordionSummary-content": { alignItems: "center" },
                  }}
                >
                  <Box display="flex" alignItems="center" gap={1}>
                    <DoneAllOutlined />
                    <Typography variant="h6">Conclusão</Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails
                  sx={{
                    backgroundColor: "#1e1e1e",
                    borderRadius: 2,
                    overflowY: "auto",
                  }}
                >
                  <ReactMarkdown>{response.conclusao}</ReactMarkdown>
                </AccordionDetails>
              </Accordion>

              {/* Resultado Consulta */}
              <Accordion
                defaultExpanded={false}
                disableGutters
                sx={{
                  borderRadius: 2,
                  backgroundColor: "transparent",
                  color: "#fff",
                  boxShadow: "none",
                  "&.Mui-expanded": { backgroundColor: "#1e1e1e" },
                }}
              >
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon sx={{ color: "#fff" }} />}
                  sx={{
                    backgroundColor: "#1e1e1e",
                    borderRadius: 2,
                    "& .MuiAccordionSummary-content": { alignItems: "center" },
                  }}
                >
                  <Box display="flex" alignItems="center" gap={1}>
                    <ArticleOutlined />
                    <Typography variant="h6">Resultado da Consulta</Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails
                  sx={{
                    backgroundColor: "#1e1e1e",
                    borderRadius: 2,
                    maxWidth: "100%",
                    overflowX: "auto",
                  }}
                >
                  <Box sx={{ width: "100%" }}>
                    <SyntaxHighlighter
                      language="json"
                      style={vscDarkPlus}
                      customStyle={{
                        borderRadius: 8,
                        whiteSpace: "pre-wrap",
                        wordBreak: "break-word",
                        overflowX: "auto",
                        maxWidth: "100%",
                      }}
                    >
                      {JSON.stringify(response.resultado, null, 2)}
                    </SyntaxHighlighter>
                    <Box textAlign="right">
                      <Tooltip title="Copiar">
                        <IconButton
                          onClick={() =>
                            handleCopiarTexto(
                              JSON.stringify(response.resultado, null, 2)
                            )
                          }
                          size="small"
                        >
                          <ContentCopyIcon sx={{ color: "#fff" }} />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </Box>
                </AccordionDetails>
              </Accordion>

              {/* Query Gerada */}
              <Accordion
                defaultExpanded={false}
                disableGutters
                sx={{
                  borderRadius: 2,
                  backgroundColor: "transparent",
                  color: "#fff",
                  boxShadow: "none",
                  "&.Mui-expanded": { backgroundColor: "#1e1e1e" },
                }}
              >
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon sx={{ color: "#fff" }} />}
                  sx={{
                    backgroundColor: "#1e1e1e",
                    borderRadius: 2,
                    minWidth: "100px",
                    "& .MuiAccordionSummary-content": { alignItems: "center" },
                  }}
                >
                  <Box display="flex" alignItems="center" gap={1}>
                    <CodeOutlined />
                    <Typography variant="h6">Query Gerada</Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails
                  sx={{ backgroundColor: "#1e1e1e", borderRadius: 2 }}
                >
                  <Box sx={{ overflow: "auto", maxWidth: "md" }}>
                    <SyntaxHighlighter
                      language="json"
                      style={vscDarkPlus}
                      customStyle={{ borderRadius: 8 }}
                    >
                      {JSON.stringify(response.queryGerada, null, 2)}
                    </SyntaxHighlighter>
                    <Box textAlign="right">
                      <Tooltip title="Copiar">
                        <IconButton
                          onClick={() =>
                            handleCopiarTexto(
                              JSON.stringify(response.queryGerada, null, 2)
                            )
                          }
                          size="small"
                        >
                          <ContentCopyIcon sx={{ color: "#fff" }} />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </Box>
                </AccordionDetails>
              </Accordion>

              <Box display="flex" mt={4} justifyContent="flex-end" gap={2}>
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={() => setResponse(null)}
                >
                  Nova Análise
                </Button>
                <Button variant="contained" color="primary" onClick={handleAsk}>
                  Consultar Novamente
                </Button>
              </Box>
            </Box>
          )}
        </Paper>
      </Container>
    </>
  );
}
