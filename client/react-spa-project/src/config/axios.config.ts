import axios, {
  AxiosError,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from "axios";
import { userManager } from "./auth.config";
import { Logger } from "@/config/logger";

const axiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_BACKEND,
});

export const axiosInstanceUpload = axios.create({
  headers: {
    "Content-Type": "multipart/form-data",
  },
});

axiosInstance.interceptors.request.use(
  (request: InternalAxiosRequestConfig) => {
    const oidcStorage = sessionStorage.getItem(
      `oidc.user:${process.env.NEXT_PUBLIC_KEYCLOAK_URL}:${process.env.NEXT_PUBLIC_KEYCLOAK_CLIENT_ID}`
    );
    if (oidcStorage === null) {
      Logger.info("access_token não encotrado");
      return request;
    }

    const oidcStorageObject = JSON.parse(oidcStorage);
    const accessToken = oidcStorageObject?.access_token;
    if (accessToken !== null) {
      request.headers.Authorization = `Bearer ${accessToken}`;
    }
    return request;
  },
  (error) => {
    return Promise.reject(error);
  }
);

type IDataError = {
  error: string;
};

axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => response,
  async (error: AxiosError<IDataError>) => {
    const tokenErrorMessage =
      error.response?.data.error === "Erro ao verificar o token";
    if (error.response?.status === 401 || tokenErrorMessage) {
      const user = await userManager.getUser();
      if (user) {
        await userManager.signoutRedirect({
          id_token_hint: user.id_token,
        });
      }
    }
    return Promise.reject(error);
  }
);

export default axiosInstance;
