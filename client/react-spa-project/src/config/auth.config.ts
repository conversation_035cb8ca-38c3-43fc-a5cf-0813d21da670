import { User, UserManager, UserManagerSettings } from "oidc-client-ts";

const settings: UserManagerSettings = {
  // Url do provedor OIDC/OAuth2
  authority:
    process.env.NEXT_PUBLIC_KEYCLOAK_URL ??
    "https://sso-interno.authcorp.dev.awsporto.auth/realms/addev",

  // client id cadastrado no RHSSO (único para cada aplicação)
  client_id: process.env.NEXT_PUBLIC_KEYCLOAK_CLIENT_ID ?? "addev",

  // Url de redirecionamento após excutação do login
  redirect_uri:
    process.env.NEXT_PUBLIC_KEYCLOAK_REDIRECT_URI ??
    "http://localhost:3000/react-spa-ccpe-cnsl-insights-execucoes/",

  // Executa o refresh token automaticamente
  automaticSilentRenew: true,

  // Executa o refresh token 30 segundos antes do access_token expirar
  accessTokenExpiringNotificationTimeInSeconds: 30,

  // Executa a revogação dos tokens ao realizar o logout
  revokeTokensOnSignout: true,

  // Url de redirecionamento após excutação do logout
  post_logout_redirect_uri:
    "http://localhost:3000/react-spa-ccpe-cnsl-insights-execucoes/",
};

export const userManager = new UserManager(settings);

// Método para limpar url após execução do login
export const onSigninCallback = (user: User | void): void => {
  window.history.replaceState({}, document.title, window.location.pathname);
};
