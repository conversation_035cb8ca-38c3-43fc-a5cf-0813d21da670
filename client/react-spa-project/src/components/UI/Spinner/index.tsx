import styled from "styled-components";
import { PortoTheme } from "@/shared/themes/Porto";
import { CircularProgress } from "@mui/material";

export const Spinner = () => {
  return (
    <SpinnerContainer>
      <CircularProgress />
    </SpinnerContainer>
  );
};

const SpinnerContainer = styled.div`
  position: absolute;
  top: 0;
  right: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: ${PortoTheme.palette.neutral_1.light};
`;
