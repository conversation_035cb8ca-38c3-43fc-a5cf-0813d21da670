import { Breadcrumbs, Typography } from "@mui/material";
import HomeIcon from "@mui/icons-material/Home";
import { Component } from "react";
import Link from "next/link";

interface BreadcrumbsProps {
  configs: {
    titulo: string;
    subtitulo: string;
    icone: React.JSX.Element;
  };
}

export const CpeBreadcrumbs = ({ configs }: BreadcrumbsProps) => {
  return (
    <div role="presentation" style={{ padding: "40px" }}>
      <Typography
        variant="h6"
        sx={{ marginBottom: "10px", fontWeight: "bold", fontSize: "20px" }}
      >
        {configs.titulo}
      </Typography>
      <Breadcrumbs aria-label="breadcrumb" separator=">">
        <Link href="/" style={{ textDecoration: "none" }}>
          <Typography
            sx={{
              color: "text.primary",
              display: "flex",
              alignItems: "center",
              ":hover": {
                textDecoration: "underline",
              },
            }}
          >
            <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Home
          </Typography>
        </Link>
        <Typography
          sx={{ color: "text.primary", display: "flex", alignItems: "center" }}
        >
          {configs.icone}
          {configs.subtitulo}
        </Typography>
      </Breadcrumbs>
    </div>
  );
};
