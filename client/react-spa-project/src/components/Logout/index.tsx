import {
  Typography,
  Box,
  Backdrop,
  LinearProgress,
  linearProgressClasses,
  styled,
} from "@mui/material";
import Image from "next/image";

export function Logout() {
  const BorderLinearProgress = styled(LinearProgress)(({ theme }) => ({
    height: 10,
    borderRadius: 5,
    [`&.${linearProgressClasses.colorPrimary}`]: {
      backgroundColor: theme.palette.grey[200],
      ...theme.applyStyles("dark", {
        backgroundColor: theme.palette.grey[800],
      }),
    },
    [`& .${linearProgressClasses.bar}`]: {
      borderRadius: 5,
      backgroundColor: "#1a90ff",
      ...theme.applyStyles("dark", {
        backgroundColor: "#308fe8",
      }),
    },
  }));

  return (
    <Backdrop
      open={true}
      sx={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        zIndex: 1301,
        backgroundColor: "#121212",
        color: "#fff",
      }}
    >
      <Box sx={{ textAlign: "center" }}>
        <Typography
          style={{
            fontWeight: "bolder",
            fontSize: "30px",
            marginTop: "25px",
            marginBottom: "5px",
          }}
        >
          Estamos encerrando sua sessão!
        </Typography>
        <Typography variant="h6" sx={{ mb: 3 }}>
          Até logo...
        </Typography>
        <BorderLinearProgress color="secondary" />
      </Box>

      <Typography variant="h6" sx={{ mt: 3 }}>
        Cloud Platform Engineering
      </Typography>
    </Backdrop>
  );
}
