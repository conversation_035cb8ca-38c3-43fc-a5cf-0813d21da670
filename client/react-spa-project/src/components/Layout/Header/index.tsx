import { Box, Button, Typography } from '@mui/material';
import { useRouter } from 'next/router';
import Link from 'next/link';
import LogoutIcon from '@mui/icons-material/Logout';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import { Poppins } from 'next/font/google';
import logoPorto from '../../../public/logo-porto-seguro.png';

const poppins = Poppins({ subsets: ['latin'], weight: ['400', '700'] });

export default function Header() {
  const router = useRouter();

  return (
    <Box sx={{
      display: 'flex',
      background: '#121212',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      color: '#fff'
    }}>
      <Link href="/" style={{ textDecoration: 'none' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <img src={logoPorto.src} alt="Logo Porto" width={150} />
          <Typography
            sx={{
              color: '#fff',
              fontWeight: 550,
              fontSize: '20px',
              marginTop: '4px',
            }}
            className={poppins.className}
          >
            Insights Operacionais
          </Typography>
        </Box>
      </Link>

      <Box sx={{ paddingRight: '20px' }}>
        <Button color="inherit" startIcon={<LogoutIcon />} onClick={() => router.push('/logout')}>
          Sair
        </Button>
      </Box>
    </Box>
  );
}