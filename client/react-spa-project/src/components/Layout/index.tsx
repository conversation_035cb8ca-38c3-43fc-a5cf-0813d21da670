import { useRouter } from "next/router";
import { PropsWithChildren, useEffect, useState } from "react";
import { hasAuthParams, useAuth } from "react-oidc-context";
import { Spinner } from "@/components/UI/Spinner";
import { Box, CssBaseline } from "@mui/material";
import Header from "./Header";
import Sidebar from "./Sidebar";

export function Layout({ children }: Readonly<PropsWithChildren>) {
  const [open, setOpen] = useState(true);

  const auth = useAuth();
  const router = useRouter();
  const pathname =
    router.pathname.split("/")[1] !== "" ? router.pathname.split("/")[1] : "/";

  if (!auth.isAuthenticated && !auth.isLoading && !hasAuthParams()) {
    auth.signinRedirect();
    return <Spinner />;
  }

  if (auth.isLoading) {
    return <Spinner />;
  }

  if (pathname === "logout") {
    return (
      <Box>
        <CssBaseline />
        {children}
      </Box>
    );
  }

  return (
    <Box sx={{ display: "flex", flexDirection: "column", minHeight: "100vh" }}>
      <CssBaseline />

      <Header />

      <Box
        component="main"
        sx={{
          backgroundColor: "#eef4ef",
          flexGrow: 1,
          height: "100%",
          overflow: "auto",
          paddingBottom: "60px",
        }}
      >
        {children}
      </Box>

      <Sidebar />
    </Box>
  );
}
