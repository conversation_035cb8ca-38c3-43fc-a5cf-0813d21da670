import { createTheme } from "@mui/material";
import { grey } from "@mui/material/colors";

declare module "@mui/material/styles" {
  interface Palette {
    neutral_1: Palette["primary"];
    red: Palette["primary"];
  }
  interface PaletteOptions {
    neutral_1?: PaletteOptions["primary"];
    red?: PaletteOptions["primary"];
  }
  interface BreakpointOverrides {
    xs: false; // removes the `xs` breakpoint
    sm: false;
    md: false;
    lg: false;
    xl: false;
    mobile: true; // adds the `mobile` breakpoint
    tablet: true;
    desktop: true;
  }
}

export const PortoTheme = createTheme({
  palette: {
    primary: {
      main: "#0046C0",
      dark: "#2662C9",
      light: "#00A1FC",
      contrastText: "#ffffff",
    },
    secondary: {
      main: "#00A1FC",
      dark: "#1381E2",
      light: "#8CD7FD",
      contrastText: "#000000",
    },
    error: {
      main: "#FF5A40",
      dark: "#A6341D",
      light: "#FF9C8C",
      contrastText: "#000000",
    },
    neutral_1: {
      main: "#909099",
      dark: "#5F5F5F",
      light: "#C8C8C8",
      contrastText: "#F1F1F1",
    },
    red: {
      main: "#FF5A40",
      dark: grey[900],
      light: grey[200],
      contrastText: "#000000",
    },
    background: {
      paper: "#ffffff",
      default: "#EEF4EF",
    },
  },
  breakpoints: {
    values: {
      mobile: 0,
      tablet: 640,
      desktop: 1024,
    },
  },
  typography: {
    fontFamily: [
      "Poppins",
      "PortoRoobert_Regular",
      "PortoRoobert_Light",
      "PortoRoobert_Bold",
      "sans-serif",
    ].join(","),
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: `
        @font-face {
          font-family: 'PortoRoobert_Regular';
          font-style: normal;
          font-display: swap;
          font-weight: 400;
          src: local('PortoRoobert_Regular'), url('/fonts/PortoRoobert_Regular.ttf') format('ttf');
          unicodeRange: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U-FEFF;
        }
        @font-face {
          font-family: 'PortoRoobert_Light';
          font-style: normal;
          font-display: swap;
          font-weight: 400;
          src: local('PortoRoobert_Light'), url('/fonts/PortoRoobert-Light.ttf') format('ttf');
          unicodeRange: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U-FEFF;
        }
        @font-face {
          font-family: 'PortoRoobert_Bold';
          font-style: normal;
          font-display: swap;
          font-weight: 400;
          src: local('PortoRoobert_Bold'), url('/fonts/PortoRoobert-Bold.ttf') format('ttf');
          unicodeRange: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U-FEFF;
        }
      `,
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: "none",
          fontWeight: "bold",
          fontSize: "16px",
          lineHeight: "20px",
          letterSpacing: "0.4px",
          borderRadius: "4px",
        },
      },
    },
  },
});
